# 更新日志

本文档记录了 TT Quant 项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增

- 开发工具链配置完善
- CI/CD 流水线配置 (计划中)

### 变更

- 优化项目文档结构和内容
- 更新 README.md 开发命令文档，包含完整的 alias 说明

### 修复

- 修复 `mix code.fix` 命令中无效的 `--auto-gen-config` 参数
- 优化 Rust 检查命令的目录切换逻辑

## [0.1.0-dev] - 2025-08-28

### 新增

- **基础架构搭建**

  - Phoenix 项目初始化，使用 `--binary-id` 和 `--live` 选项
  - 完整的 Phoenix LiveView 配置
  - 数据库集成 (PostgreSQL + Ecto)
  - 前端资源构建 (esbuild + Tailwind CSS)
  - DaisyUI 和 Heroicons 集成

- **Rustler NIF 集成**

  - 添加 Rustler 依赖 (v0.36.2)
  - 创建 `tt_quant_core` Rust crate
  - 配置 Rust 工作区 (`Cargo.toml`)
  - 实现基础 NIF 接口 (`TtQuant.Core.add/2`)
  - macOS 特定的 Rust 编译配置

- **开发工具链**

  - 代码质量检查工具 (Credo v1.7, Dialyxir v1.4)
  - 自动化开发命令 (`mix code.check`, `mix rust.check` 等)
  - VSCode 配置和拼写检查
  - 预提交检查流程

- **项目文档**
  - 综合性项目文档 (`tt_quant_docs/`)
  - 架构设计文档 (ARCHITECTURE_DESIGN.md)
  - 8 阶段开发计划 (DEVELOPMENT_PLAN.md)
  - 技术决策分析 (TECHNICAL_DECISIONS.md)
  - Nautilus Trader 系统分析 (NAUTILUS_TRADER_ANALYSIS.md)
  - 完整的 README 文档

### 技术栈

- **后端**: Elixir 1.15+ / Phoenix 1.8.0
- **核心引擎**: Rust 1.70+ / Rustler 0.36.2
- **数据库**: PostgreSQL (Ecto 3.13)
- **前端**: LiveView 1.1.0 / Tailwind CSS / DaisyUI
- **构建工具**: esbuild 0.10 / Mix

### 项目结构

```
tt_quant/
├── lib/tt_quant/           # 核心业务逻辑
├── lib/tt_quant_web/       # Web 层和 LiveView
├── native/tt_quant_core/   # Rust NIF 实现
├── config/                 # 应用配置
├── assets/                 # 前端资源
├── test/                   # 测试套件
└── tt_quant_docs/          # 项目文档
```

### 已验证功能

- Phoenix 服务器启动和基础路由
- Rustler NIF 编译和加载
- 基础 NIF 函数调用 (`TtQuant.Core.add/2`)
- 开发环境热重载
- 代码格式化和质量检查

### 开发进度

- ✅ **第一阶段 1.1**: 项目初始化 (75% 完成)
  - ✅ Phoenix 项目创建
  - ✅ Rustler 集成和 NIF 配置
  - 🚧 开发环境和工具链设置
  - ⏳ CI/CD 流水线配置

### 下一步计划

- 完成开发环境和工具链配置
- 实现核心 Rust 引擎框架
- 建立 Phoenix 基础服务
- 集成测试框架搭建

---

## 版本说明

- **[未发布]**: 正在开发中的功能
- **[0.1.0-dev]**: 开发版本，包含基础架构和 NIF 集成

## 贡献指南

请在提交 Pull Request 时更新此更新日志，并遵循以下格式：

- **新增**: 新功能
- **变更**: 现有功能的变更
- **弃用**: 即将移除的功能
- **移除**: 已移除的功能
- **修复**: Bug 修复
- **安全**: 安全相关的修复

每个条目应该简洁明了，并包含相关的 issue 或 PR 链接（如果适用）。
